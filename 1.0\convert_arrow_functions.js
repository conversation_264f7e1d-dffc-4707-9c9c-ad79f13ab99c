// 将箭头函数转换为传统函数的脚本
const fs = require('fs');

function convertArrowFunctions(code) {
    // 简单的箭头函数转换
    // 注意：这是一个简化的转换，可能不能处理所有复杂情况
    
    // 1. 处理简单的箭头函数 (e) => { ... }
    code = code.replace(/\(([^)]*)\)\s*=>\s*\{/g, 'function($1) {');
    
    // 2. 处理单参数箭头函数 e => { ... }
    code = code.replace(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=>\s*\{/g, 'function($1) {');
    
    // 3. 处理简单的表达式箭头函数 (e) => expression
    code = code.replace(/\(([^)]*)\)\s*=>\s*([^;,\n}]+)/g, 'function($1) { return $2; }');
    
    // 4. 处理单参数表达式箭头函数 e => expression
    code = code.replace(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=>\s*([^;,\n}]+)/g, 'function($1) { return $2; }');
    
    // 5. 处理无参数箭头函数 () => { ... }
    code = code.replace(/\(\s*\)\s*=>\s*\{/g, 'function() {');
    
    // 6. 处理无参数表达式箭头函数 () => expression
    code = code.replace(/\(\s*\)\s*=>\s*([^;,\n}]+)/g, 'function() { return $1; }');
    
    return code;
}

function convertTemplateStrings(code) {
    // 将模板字符串转换为字符串拼接
    // 这是一个简化的转换
    code = code.replace(/`([^`]*\$\{[^}]+\}[^`]*)`/g, function(match, content) {
        // 简单的模板字符串转换
        let result = content.replace(/\$\{([^}]+)\}/g, '" + ($1) + "');
        return '"' + result + '"';
    });
    
    return code;
}

function convertLetConst(code) {
    // 将 let 和 const 转换为 var
    code = code.replace(/\blet\s+/g, 'var ');
    code = code.replace(/\bconst\s+/g, 'var ');
    
    return code;
}

try {
    console.log('开始转换 bundle_1.js...');
    
    // 读取原文件
    let code = fs.readFileSync('bundle_1.js', 'utf8');
    console.log(`原文件大小: ${code.length} 字符`);
    
    // 转换箭头函数
    console.log('转换箭头函数...');
    code = convertArrowFunctions(code);
    
    // 转换模板字符串
    console.log('转换模板字符串...');
    code = convertTemplateStrings(code);
    
    // 转换 let/const
    console.log('转换 let/const 为 var...');
    code = convertLetConst(code);
    
    // 保存转换后的文件
    fs.writeFileSync('bundle_1_converted.js', code, 'utf8');
    
    console.log('✅ 转换完成！');
    console.log(`转换后文件大小: ${code.length} 字符`);
    console.log('已保存为: bundle_1_converted.js');
    
    // 测试语法
    try {
        new Function(code);
        console.log('✅ 转换后的文件语法检查通过！');
    } catch (error) {
        console.log('❌ 转换后仍有语法错误：', error.message);
    }
    
} catch (error) {
    console.error('❌ 转换失败：', error.message);
}
