// JSNice 兼容性转换器
const fs = require('fs');

function makeJSNiceCompatible(code) {
    console.log('开始转换为 JSNice 兼容格式...');
    
    // 1. 移除第一行的注释（可能包含特殊字符）
    code = code.replace(/^\/\*!.*?\*\/\s*\n?/s, '');
    
    // 2. 将 IIFE 箭头函数转换为普通函数
    code = code.replace(/^\(\(\) => \{/, '(function() {');
    code = code.replace(/\}\)\(\);?\s*$/, '})();');
    
    // 3. 处理简单的箭头函数转换
    // 匹配 (params) => { body }
    code = code.replace(/\(([^)]*)\)\s*=>\s*\{/g, 'function($1) {');
    
    // 匹配 param => { body }
    code = code.replace(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=>\s*\{/g, 'function($1) {');
    
    // 匹配 () => { body }
    code = code.replace(/\(\s*\)\s*=>\s*\{/g, 'function() {');
    
    // 4. 处理箭头函数表达式（更复杂的情况）
    // 匹配 (params) => expression
    code = code.replace(/\(([^)]*)\)\s*=>\s*([^;,\n}]+[^;,\n}]*)/g, function(match, params, expr) {
        // 确保表达式不包含大括号开始
        if (expr.trim().startsWith('{')) {
            return match; // 不转换，让其他规则处理
        }
        return `function(${params}) { return ${expr}; }`;
    });
    
    // 匹配 param => expression
    code = code.replace(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=>\s*([^;,\n}]+[^;,\n}]*)/g, function(match, param, expr) {
        if (expr.trim().startsWith('{')) {
            return match;
        }
        return `function(${param}) { return ${expr}; }`;
    });
    
    // 匹配 () => expression
    code = code.replace(/\(\s*\)\s*=>\s*([^;,\n}]+[^;,\n}]*)/g, function(match, expr) {
        if (expr.trim().startsWith('{')) {
            return match;
        }
        return `function() { return ${expr}; }`;
    });
    
    // 5. 转换模板字符串为字符串拼接
    code = code.replace(/`([^`]*)`/g, function(match, content) {
        if (content.includes('${')) {
            // 包含变量插值的模板字符串
            let result = content.replace(/\$\{([^}]+)\}/g, '" + ($1) + "');
            return '"' + result + '"';
        } else {
            // 普通字符串
            return '"' + content + '"';
        }
    });
    
    // 6. 转换 let 和 const 为 var
    code = code.replace(/\blet\s+/g, 'var ');
    code = code.replace(/\bconst\s+/g, 'var ');
    
    // 7. 处理解构赋值（简单情况）
    // 这个比较复杂，暂时跳过
    
    // 8. 处理默认参数（转换为函数内部的默认值设置）
    code = code.replace(/function\s*\(([^)]*)\)\s*\{/g, function(match, params) {
        if (params.includes('=')) {
            // 有默认参数，需要转换
            var newParams = [];
            var defaultAssignments = [];
            
            params.split(',').forEach(function(param) {
                param = param.trim();
                if (param.includes('=')) {
                    var parts = param.split('=');
                    var paramName = parts[0].trim();
                    var defaultValue = parts[1].trim();
                    newParams.push(paramName);
                    defaultAssignments.push(`if (typeof ${paramName} === 'undefined') ${paramName} = ${defaultValue};`);
                } else {
                    newParams.push(param);
                }
            });
            
            var result = `function(${newParams.join(', ')}) {\n${defaultAssignments.join('\n')}`;
            return result;
        }
        return match;
    });
    
    return code;
}

try {
    console.log('读取 bundle_1.js...');
    let code = fs.readFileSync('bundle_1.js', 'utf8');
    console.log(`原文件大小: ${code.length} 字符`);
    
    // 转换为 JSNice 兼容格式
    code = makeJSNiceCompatible(code);
    
    // 保存转换后的文件
    fs.writeFileSync('bundle_1_jsnice_compatible.js', code, 'utf8');
    
    console.log('✅ 转换完成！');
    console.log(`转换后文件大小: ${code.length} 字符`);
    console.log('已保存为: bundle_1_jsnice_compatible.js');
    
    // 测试语法
    try {
        new Function(code);
        console.log('✅ 转换后的文件语法检查通过！');
        console.log('现在可以将 bundle_1_jsnice_compatible.js 上传到 JSNice 工具了。');
    } catch (error) {
        console.log('❌ 转换后仍有语法错误：', error.message);
        
        // 尝试找到错误位置
        var lines = code.split('\n');
        var errorMatch = error.message.match(/line (\d+)/i);
        if (errorMatch) {
            var lineNum = parseInt(errorMatch[1]);
            console.log(`错误行 ${lineNum}: ${lines[lineNum - 1]}`);
        }
    }
    
} catch (error) {
    console.error('❌ 转换失败：', error.message);
}
