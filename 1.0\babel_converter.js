// 使用 Babel 转换为 ES5 的脚本
const fs = require('fs');

// 检查是否安装了 @babel/core 和相关包
function checkBabelInstallation() {
    try {
        require('@babel/core');
        require('@babel/preset-env');
        return true;
    } catch (error) {
        return false;
    }
}

function convertWithBabel(code) {
    const babel = require('@babel/core');
    
    const result = babel.transformSync(code, {
        presets: [
            ['@babel/preset-env', {
                targets: {
                    ie: '11'  // 兼容 IE11，确保是 ES5
                },
                modules: false  // 保持模块格式不变
            }]
        ],
        plugins: [
            '@babel/plugin-transform-arrow-functions',
            '@babel/plugin-transform-template-literals',
            '@babel/plugin-transform-block-scoped-functions',
            '@babel/plugin-transform-block-scoping'
        ]
    });
    
    return result.code;
}

function manualConvert(code) {
    console.log('使用手动转换方法...');
    
    // 1. 转换最外层的 IIFE
    code = code.replace(/^\(\(\) => \{/, '(function() {');
    
    // 2. 转换简单的箭头函数
    // 处理 (params) => { body }
    code = code.replace(/\(([^)]*)\)\s*=>\s*\{/g, 'function($1) {');
    
    // 处理 param => { body }
    code = code.replace(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=>\s*\{/g, 'function($1) {');
    
    // 处理 () => { body }
    code = code.replace(/\(\s*\)\s*=>\s*\{/g, 'function() {');
    
    // 3. 转换简单的箭头函数表达式
    // 处理 (params) => expression
    code = code.replace(/\(([^)]*)\)\s*=>\s*([^{][^;,\n}]*)/g, 'function($1) { return $2; }');
    
    // 处理 param => expression  
    code = code.replace(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=>\s*([^{][^;,\n}]*)/g, 'function($1) { return $2; }');
    
    // 处理 () => expression
    code = code.replace(/\(\s*\)\s*=>\s*([^{][^;,\n}]*)/g, 'function() { return $1; }');
    
    // 4. 转换 let/const 为 var
    code = code.replace(/\blet\s+/g, 'var ');
    code = code.replace(/\bconst\s+/g, 'var ');
    
    // 5. 转换模板字符串
    code = code.replace(/`([^`]*)`/g, function(match, content) {
        if (content.includes('${')) {
            // 简单的变量插值转换
            let result = content.replace(/\$\{([^}]+)\}/g, '" + ($1) + "');
            return '"' + result + '"';
        } else {
            return '"' + content + '"';
        }
    });
    
    return code;
}

async function main() {
    try {
        console.log('读取 bundle_1.js...');
        let code = fs.readFileSync('bundle_1.js', 'utf8');
        console.log(`原文件大小: ${code.length} 字符`);
        
        let convertedCode;
        
        if (checkBabelInstallation()) {
            console.log('使用 Babel 进行转换...');
            try {
                convertedCode = convertWithBabel(code);
                console.log('✅ Babel 转换成功！');
            } catch (error) {
                console.log('❌ Babel 转换失败，使用手动转换：', error.message);
                convertedCode = manualConvert(code);
            }
        } else {
            console.log('未安装 Babel，使用手动转换...');
            console.log('提示：如需更好的转换效果，请运行：');
            console.log('npm install @babel/core @babel/preset-env @babel/plugin-transform-arrow-functions @babel/plugin-transform-template-literals @babel/plugin-transform-block-scoped-functions @babel/plugin-transform-block-scoping');
            convertedCode = manualConvert(code);
        }
        
        // 保存转换后的文件
        fs.writeFileSync('bundle_1_es5.js', convertedCode, 'utf8');
        
        console.log('✅ 转换完成！');
        console.log(`转换后文件大小: ${convertedCode.length} 字符`);
        console.log('已保存为: bundle_1_es5.js');
        
        // 测试语法
        try {
            new Function(convertedCode);
            console.log('✅ 转换后的文件语法检查通过！');
            console.log('现在可以将 bundle_1_es5.js 上传到 JSNice 工具了。');
        } catch (error) {
            console.log('❌ 转换后仍有语法错误：', error.message);
            
            // 提供更多建议
            console.log('\n=== 进一步的建议 ===');
            console.log('1. 尝试使用在线 JavaScript 美化工具：');
            console.log('   - https://beautifier.io/');
            console.log('   - https://codebeautify.org/jsviewer');
            console.log('2. 尝试其他反混淆工具：');
            console.log('   - https://deobfuscate.io/');
            console.log('   - https://obf-io.deobfuscate.io/');
            console.log('3. 如果文件太大，可以尝试分段处理');
        }
        
    } catch (error) {
        console.error('❌ 处理失败：', error.message);
    }
}

main();
