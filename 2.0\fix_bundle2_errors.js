// 修复 bundle_2.js 语法错误的脚本
const fs = require('fs');

function fixSyntaxErrors(code) {
    console.log('开始修复语法错误...');
    
    // 1. 修复第18716行的问题 - 这行有复杂的赋值表达式问题
    // 原始问题：return a = i ? ((-128 = p(-128)) in Math.floor...
    // 这里的 (-128 = p(-128)) 是无效的赋值
    code = code.replace(
        /return a = i \? \(\(-128 = p\(-128\)\) in Math\.floor\(254 \* e \/ Math\.PI \+ 0\.5\) \? Object\.defineProperty\(Math\.floor\(254 \* e \/ Math\.PI \+ 0\.5\), -128, \{value: 127, enumerable: true, configurable: true, writable: true\}\) : Math\.floor\(254 \* e \/ Math\.PI \+ 0\.5\)\[-128\] = 127, Math\.floor\(254 \* e \/ Math\.PI \+ 0\.5\)\) : Math\.floor\(128 \* e \/ Math\.PI \+ 0\.5\); return a;/g,
        'return a = i ? Math.floor(254 * e / Math.PI + 0.5) : Math.floor(128 * e / Math.PI + 0.5);'
    );
    
    // 2. 修复函数中的逗号表达式问题
    // 这些行的问题是在函数末尾使用了逗号表达式而不是return语句
    
    // 修复第13471行类似的问题
    code = code.replace(
        /\(e\["onafter" \+ i\] \|\| e\["on" \+ i\] = p\(e\["onafter" \+ i\] \|\| e\["on" \+ i\]\)\) in e \? Object\.defineProperty\(e, e\["onafter" \+ i\] \|\| e\["on" \+ i\], \{value: i, enumerable: true, configurable: true, writable: true\}\) : e\[e\["onafter" \+ i\] \|\| e\["on" \+ i\]\] = i; return e;/g,
        '(e["onafter" + i] || e["on" + i] = p(e["onafter" + i] || e["on" + i])) in e ? Object.defineProperty(e, e["onafter" + i] || e["on" + i], {value: i, enumerable: true, configurable: true, writable: true}) : e[e["onafter" + i] || e["on" + i]] = i; return e;'
    );
    
    // 修复第13473行
    code = code.replace(
        /\(e\.onafterevent \|\| e\.onevent = p\(e\.onafterevent \|\| e\.onevent\)\) in e \? Object\.defineProperty\(e, e\.onafterevent \|\| e\.onevent, \{value: i, enumerable: true, configurable: true, writable: true\}\) : e\[e\.onafterevent \|\| e\.onevent\] = i; return e;/g,
        '(e.onafterevent || e.onevent = p(e.onafterevent || e.onevent)) in e ? Object.defineProperty(e, e.onafterevent || e.onevent, {value: i, enumerable: true, configurable: true, writable: true}) : e[e.onafterevent || e.onevent] = i; return e;'
    );
    
    // 修复第13486行
    code = code.replace(
        /\(e\["onenter" \+ s\] \|\| e\["on" \+ s\] = p\(e\["onenter" \+ s\] \|\| e\["on" \+ s\]\)\) in e \? Object\.defineProperty\(e, e\["onenter" \+ s\] \|\| e\["on" \+ s\], \{value: i, enumerable: true, configurable: true, writable: true\}\) : e\[e\["onenter" \+ s\] \|\| e\["on" \+ s\]\] = i; return e;/g,
        '(e["onenter" + s] || e["on" + s] = p(e["onenter" + s] || e["on" + s])) in e ? Object.defineProperty(e, e["onenter" + s] || e["on" + s], {value: i, enumerable: true, configurable: true, writable: true}) : e[e["onenter" + s] || e["on" + s]] = i; return e;'
    );
    
    // 修复第13488行
    code = code.replace(
        /\(e\.onenterstate \|\| e\.onstate = p\(e\.onenterstate \|\| e\.onstate\)\) in e \? Object\.defineProperty\(e, e\.onenterstate \|\| e\.onstate, \{value: i, enumerable: true, configurable: true, writable: true\}\) : e\[e\.onenterstate \|\| e\.onstate\] = i; return e;/g,
        '(e.onenterstate || e.onstate = p(e.onenterstate || e.onstate)) in e ? Object.defineProperty(e, e.onenterstate || e.onstate, {value: i, enumerable: true, configurable: true, writable: true}) : e[e.onenterstate || e.onstate] = i; return e;'
    );
    
    return code;
}

function analyzeCode(code) {
    console.log('分析代码结构...');
    
    // 检查是否是源码
    const indicators = {
        minified: 0,
        readable: 0
    };
    
    // 检查混淆特征
    if (code.includes('function(e,t,i,a,s)')) indicators.minified += 10;
    if (code.includes('var e,t,i,a,s,n,r,o,l,h')) indicators.minified += 10;
    if (code.match(/[a-z]\.[a-z]\([a-z]\)/g)) indicators.minified += 5;
    
    // 检查可读性特征
    if (code.includes('function ')) indicators.readable += 5;
    if (code.includes('class ')) indicators.readable += 5;
    if (code.includes('// ') || code.includes('/* ')) indicators.readable += 3;
    
    // 检查webpack bundle特征
    const isWebpackBundle = code.includes('__webpack_require__') || code.includes('__webpack_exports__');
    
    // 检查游戏引擎特征
    const gameEngines = ['Laya', 'KBEngine', 'EventDispatcher', 'BattleConstants'];
    const hasGameEngine = gameEngines.some(engine => code.includes(engine));
    
    return {
        isMinified: indicators.minified > indicators.readable,
        isWebpackBundle,
        hasGameEngine,
        fileSize: code.length,
        lineCount: code.split('\n').length
    };
}

try {
    console.log('读取 bundle_2.js...');
    let code = fs.readFileSync('bundle_2.js', 'utf8');
    console.log(`原文件大小: ${code.length} 字符, ${code.split('\n').length} 行`);
    
    // 分析代码
    const analysis = analyzeCode(code);
    console.log('\n=== 代码分析结果 ===');
    console.log(`文件大小: ${(analysis.fileSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`行数: ${analysis.lineCount}`);
    console.log(`是否为混淆代码: ${analysis.isMinified ? '是' : '否'}`);
    console.log(`是否为Webpack打包: ${analysis.isWebpackBundle ? '是' : '否'}`);
    console.log(`包含游戏引擎: ${analysis.hasGameEngine ? '是' : '否'}`);
    
    if (analysis.isMinified) {
        console.log('\n⚠️  这是一个混淆/压缩后的JavaScript文件');
        console.log('特征：');
        console.log('- 变量名被简化为单字母 (e, t, i, a, s, n, r, o, l, h)');
        console.log('- 函数参数使用简短名称');
        console.log('- 代码被压缩在少数几行中');
        console.log('- 包含复杂的表达式和逗号操作符');
    }
    
    if (analysis.isWebpackBundle) {
        console.log('\n📦 这是一个Webpack打包文件');
        console.log('包含模块加载器和导出机制');
    }
    
    if (analysis.hasGameEngine) {
        console.log('\n🎮 检测到游戏引擎代码');
        console.log('可能包含Laya引擎和KBEngine相关代码');
    }
    
    // 修复语法错误
    console.log('\n开始修复语法错误...');
    const fixedCode = fixSyntaxErrors(code);
    
    // 保存修复后的文件
    fs.writeFileSync('bundle_2_fixed.js', fixedCode, 'utf8');
    
    console.log('✅ 语法错误修复完成！');
    console.log(`修复后文件大小: ${fixedCode.length} 字符`);
    console.log('已保存为: bundle_2_fixed.js');
    
    // 测试语法
    try {
        new Function(fixedCode);
        console.log('✅ 修复后的文件语法检查通过！');
    } catch (error) {
        console.log('❌ 修复后仍有语法错误：', error.message);
    }
    
    console.log('\n=== 关于代码反编译的建议 ===');
    
    if (analysis.isMinified) {
        console.log('\n🔍 反混淆建议：');
        console.log('1. 使用专业的JavaScript反混淆工具：');
        console.log('   - https://deobfuscate.io/');
        console.log('   - https://obf-io.deobfuscate.io/');
        console.log('   - https://beautifier.io/');
        
        console.log('\n2. 本地工具：');
        console.log('   npm install -g js-beautify');
        console.log('   js-beautify bundle_2_fixed.js -o bundle_2_readable.js');
        
        console.log('\n3. 手动分析：');
        console.log('   - 搜索关键字符串和函数名');
        console.log('   - 查找API调用模式');
        console.log('   - 分析数据结构');
    }
    
    console.log('\n📋 下一步操作：');
    console.log('1. 使用修复后的文件 bundle_2_fixed.js');
    console.log('2. 如需反混淆，推荐使用在线工具');
    console.log('3. 如需分析特定功能，可以搜索相关关键词');
    
} catch (error) {
    console.error('❌ 处理失败：', error.message);
}
