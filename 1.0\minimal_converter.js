// 最小化转换器 - 只处理最基本的兼容性问题
const fs = require('fs');

function minimalConvert(code) {
    console.log('进行最小化转换...');
    
    // 1. 只转换最外层的 IIFE 箭头函数
    if (code.startsWith('/*! For license information please see bundle.js.LICENSE.txt */\n(() => {')) {
        console.log('转换最外层 IIFE...');
        code = code.replace(/^(\/\*! For license information please see bundle\.js\.LICENSE\.txt \*\/\s*\n)\(\(\) => \{/, '$1(function() {');
        
        // 找到最后的 })(); 并替换
        var lastIndex = code.lastIndexOf('})();');
        if (lastIndex !== -1) {
            code = code.substring(0, lastIndex) + '})();' + code.substring(lastIndex + 5);
        }
    }
    
    // 2. 转换简单的 let/const 为 var（只在行首的情况）
    code = code.replace(/^\s*let\s+/gm, function(match) {
        return match.replace('let', 'var');
    });
    code = code.replace(/^\s*const\s+/gm, function(match) {
        return match.replace('const', 'var');
    });
    
    // 3. 转换简单的模板字符串（不包含复杂表达式的）
    code = code.replace(/`([^`$]*)`/g, '"$1"');
    
    return code;
}

try {
    console.log('读取 bundle_1.js...');
    let code = fs.readFileSync('bundle_1.js', 'utf8');
    console.log(`原文件大小: ${code.length} 字符`);
    
    // 进行最小化转换
    code = minimalConvert(code);
    
    // 保存转换后的文件
    fs.writeFileSync('bundle_1_minimal.js', code, 'utf8');
    
    console.log('✅ 最小化转换完成！');
    console.log(`转换后文件大小: ${code.length} 字符`);
    console.log('已保存为: bundle_1_minimal.js');
    
    // 测试语法
    try {
        new Function(code);
        console.log('✅ 转换后的文件语法检查通过！');
        console.log('现在可以尝试将 bundle_1_minimal.js 上传到 JSNice 工具了。');
    } catch (error) {
        console.log('❌ 转换后仍有语法错误：', error.message);
        console.log('建议：JSNice 可能不支持某些 ES6+ 语法特性。');
        console.log('你可以尝试使用其他反编译工具，如：');
        console.log('1. Prettier (在线格式化)');
        console.log('2. JS Beautifier');
        console.log('3. 或者使用本地的 JavaScript 反混淆工具');
    }
    
} catch (error) {
    console.error('❌ 转换失败：', error.message);
}

// 额外建议
console.log('\n=== 额外建议 ===');
console.log('如果 JSNice 仍然报错，可能的原因：');
console.log('1. JSNice 不支持 ES6+ 语法（箭头函数、模板字符串、let/const 等）');
console.log('2. 文件太大（JSNice 可能有大小限制）');
console.log('3. 包含了 JSNice 不认识的语法结构');
console.log('\n建议的解决方案：');
console.log('1. 尝试使用 Babel 将代码转换为 ES5');
console.log('2. 使用其他在线反混淆工具');
console.log('3. 使用本地工具如 js-beautify 进行格式化');
