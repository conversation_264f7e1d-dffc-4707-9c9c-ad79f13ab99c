import re

def fix_js_syntax(js_code):
    # 1. 删除尾随逗号：去除数组和对象字面量中的尾随逗号
    js_code = re.sub(r',\s*}', '}', js_code)  # 删除对象字面量中的尾随逗号
    js_code = re.sub(r',\s*]', ']', js_code)  # 删除数组字面量中的尾随逗号
    
    # 2. 补充缺失的分号：检查行末尾是否缺少分号
    js_code = re.sub(r'([^\n;])\n', r'\1;\n', js_code)  # 在行末添加分号（排除已存在的分号）
    
    # 3. 检查并修复括号不匹配：确保每个开括号都有对应的闭括号
    # 这个是较复杂的操作，可以考虑逐步检查括号的配对
    open_parentheses = js_code.count('(')
    close_parentheses = js_code.count(')')
    
    if open_parentheses != close_parentheses:
        print(f"Warning: There are {open_parentheses} open parentheses and {close_parentheses} close parentheses. Check for unbalanced parentheses.")
        # 这里可以选择自动修复或进一步处理，例如删除多余的括号
        # 由于修复逻辑较复杂，通常需要人工检查。

    # 4. 修复常见的语法错误，如缺失的闭合花括号（如果可能）
    js_code = re.sub(r'(\w+)\s*{\s*}', r'\1 {}', js_code)  # 修复没有操作体的空花括号
    
    # 5. 返回修复后的代码
    return js_code

# 读取格式化后的 JavaScript 文件
def process_js_file(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        js_code = f.read()
    
    # 修复 JavaScript 代码的常见问题
    fixed_js_code = fix_js_syntax(js_code)
    
    # 保存修复后的代码到新的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(fixed_js_code)

# 使用示例
input_file = './bundle_1.js'  # 您的格式化后文件路径
output_file = './fixed_bundle.js'  # 输出修复后的文件路径

process_js_file(input_file, output_file)
print(f"Fixed JS code saved to {output_file}")
